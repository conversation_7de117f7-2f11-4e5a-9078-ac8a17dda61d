'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, Filter, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';

const EditRequestFilters = ({ filters, setFilters }) => {
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((state) => state.employee);
	console.log(employees);

	const [dateRange, setDateRange] = useState({
		from: null,
		to: null,
	});

	// Fetch employees on component mount
	useEffect(() => {
		dispatch(fetchAllEmployees());
	}, [dispatch]);

	// Status options
	const statusOptions = [
		{ value: 'all', label: 'All Statuses' },
		{ value: 'pending', label: 'Pending' },
		{ value: 'approved', label: 'Approved' },
		{ value: 'rejected', label: 'Rejected' },
	];

	// Section options based on the schema
	const sectionOptions = [
		{ value: 'all', label: 'All Sections' },
		{ value: 'personal-details', label: 'Personal Details' },
		{ value: 'family-details', label: 'Family Details' },
		{ value: 'education-details', label: 'Education Details' },
		{ value: 'experience-details', label: 'Experience Details' },
		{ value: 'contact-details', label: 'Contact Details' },
		// { value: 'employment-details', label: 'Employment Details' },
		// { value: 'earnings-details', label: 'Earnings Details' },
		// { value: 'benefits', label: 'Benefits' },
		// { value: 'skills', label: 'Skills' },
		// { value: 'equipment', label: 'Equipment' },
		// { value: 'company-details', label: 'Company Details' },
	];

	// Employee options from Redux store
	const employeeOptions = [
		{ value: 'all', label: 'All Employees' },
		...(employees || [])
			.filter((employee) => employee?.personalDetails?.nameOnNRIC)
			.map((employee) => ({
				value: employee?._id,
				label: `${employee?.personalDetails?.nameOnNRIC} (${employee?.personalDetails?.employeeOrgId})`,
			})),
	];

	const handleFilterChange = (key, value) => {
		setFilters((prev) => ({
			...prev,
			[key]: value,
		}));
	};

	const handleDateRangeChange = (range) => {
		setDateRange(range);
		setFilters((prev) => ({
			...prev,
			dateRange: range,
		}));
	};

	const clearFilters = () => {
		setFilters({
			status: 'all',
			section: 'all',
			employee: 'all',
			dateRange: null,
		});
		setDateRange({ from: null, to: null });
	};

	const hasActiveFilters =
		filters.status !== 'all' ||
		filters.section !== 'all' ||
		filters.employee !== 'all' ||
		filters.dateRange;

	return (
		<div className="space-y-4 mb-6">
			<div className="flex items-center gap-2">
				<Filter className="h-4 w-4 text-muted-foreground" />
				<Label className="text-sm font-medium">Filters</Label>
				{hasActiveFilters && (
					<Button
						variant="ghost"
						size="sm"
						onClick={clearFilters}
						className="h-6 px-2 text-xs"
					>
						<X className="h-3 w-3 mr-1" />
						Clear
					</Button>
				)}
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				{/* Status Filter */}
				<div className="space-y-2">
					<Label
						htmlFor="status-filter"
						className="text-xs text-muted-foreground"
					>
						Status
					</Label>
					<Select
						value={filters.status}
						onValueChange={(value) => handleFilterChange('status', value)}
					>
						<SelectTrigger id="status-filter">
							<SelectValue placeholder="Select status" />
						</SelectTrigger>
						<SelectContent>
							{statusOptions.map((option) => (
								<SelectItem key={option.value} value={option.value}>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{/* Section Filter */}
				<div className="space-y-2">
					<Label
						htmlFor="section-filter"
						className="text-xs text-muted-foreground"
					>
						Section
					</Label>
					<Select
						value={filters.section}
						onValueChange={(value) => handleFilterChange('section', value)}
					>
						<SelectTrigger id="section-filter">
							<SelectValue placeholder="Select section" />
						</SelectTrigger>
						<SelectContent>
							{sectionOptions.map((option) => (
								<SelectItem key={option.value} value={option.value}>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{/* Employee Filter */}
				<div className="space-y-2">
					<Label
						htmlFor="employee-filter"
						className="text-xs text-muted-foreground"
					>
						Employee
					</Label>
					<Select
						value={filters.employee}
						onValueChange={(value) => handleFilterChange('employee', value)}
					>
						<SelectTrigger id="employee-filter">
							<SelectValue placeholder="Select employee" />
						</SelectTrigger>
						<SelectContent>
							{employeeOptions.map((option) => (
								<SelectItem key={option.value} value={option.value}>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{/* Date Range Filter */}
				<div className="space-y-2">
					<Label className="text-xs text-muted-foreground">Date Range</Label>
					<Popover>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									'w-full justify-start text-left font-normal',
									!dateRange?.from && 'text-muted-foreground'
								)}
							>
								<CalendarIcon className="mr-2 h-4 w-4" />
								{dateRange?.from ? (
									dateRange.to ? (
										<>
											{format(dateRange.from, 'LLL dd, y')} -{' '}
											{format(dateRange.to, 'LLL dd, y')}
										</>
									) : (
										format(dateRange.from, 'LLL dd, y')
									)
								) : (
									<span>Pick a date range</span>
								)}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0" align="start">
							<Calendar
								initialFocus
								mode="range"
								defaultMonth={dateRange?.from}
								selected={dateRange}
								onSelect={handleDateRangeChange}
								numberOfMonths={2}
							/>
						</PopoverContent>
					</Popover>
				</div>
			</div>
		</div>
	);
};

export default EditRequestFilters;
