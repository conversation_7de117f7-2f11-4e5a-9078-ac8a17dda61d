'use client';

import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  BarChart3,
  Edit,
  Eye,
  Trash2,
} from 'lucide-react';

/**
 * ProjectContextMenu Component
 * Context menu for project cards with Summary, Edit, View, and Delete options
 */
export const ProjectContextMenu = ({ 
  children, 
  project, 
  onViewSummary, 
  onEdit, 
  onView, 
  onDelete 
}) => {
  const handleMenuItemClick = (action, e) => {
    e.stopPropagation();
    action?.(project);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={(e) => handleMenuItemClick(onViewSummary, e)}
          className="cursor-pointer"
        >
          <BarChart3 className="h-4 w-4 mr-2" />
          Summary
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => handleMenuItemClick(onEdit, e)}
          className="cursor-pointer"
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Project
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => handleMenuItemClick(onView, e)}
          className="cursor-pointer"
        >
          <Eye className="h-4 w-4 mr-2" />
          View Project
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => handleMenuItemClick(onDelete, e)}
          className="cursor-pointer text-destructive focus:text-destructive"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete Project
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
