/**
 * Background images data for project cards and kanban boards
 * Using high-resolution landscape images (1920x1080, 16:9 ratio) for optimal quality
 */

export const backgroundImages = [
  {
    id: 1,
    name: 'Mountain Lake',
    url: 'https://picsum.photos/1920/1080?random=1',
    thumbnail: 'https://picsum.photos/400/225?random=1',
    category: 'nature'
  },
  {
    id: 2,
    name: 'Forest Path',
    url: 'https://picsum.photos/1920/1080?random=2',
    thumbnail: 'https://picsum.photos/400/225?random=2',
    category: 'nature'
  },
  {
    id: 3,
    name: 'Ocean Sunset',
    url: 'https://picsum.photos/1920/1080?random=3',
    thumbnail: 'https://picsum.photos/400/225?random=3',
    category: 'nature'
  },
  {
    id: 4,
    name: 'City Skyline',
    url: 'https://picsum.photos/1920/1080?random=4',
    thumbnail: 'https://picsum.photos/400/225?random=4',
    category: 'urban'
  },
  {
    id: 5,
    name: 'Desert Landscape',
    url: 'https://picsum.photos/1920/1080?random=5',
    thumbnail: 'https://picsum.photos/400/225?random=5',
    category: 'nature'
  },
  {
    id: 6,
    name: 'Modern Architecture',
    url: 'https://picsum.photos/1920/1080?random=6',
    thumbnail: 'https://picsum.photos/400/225?random=6',
    category: 'urban'
  },
  {
    id: 7,
    name: 'Rolling Hills',
    url: 'https://picsum.photos/1920/1080?random=7',
    thumbnail: 'https://picsum.photos/400/225?random=7',
    category: 'nature'
  },
  {
    id: 8,
    name: 'Coastal Cliffs',
    url: 'https://picsum.photos/1920/1080?random=8',
    thumbnail: 'https://picsum.photos/400/225?random=8',
    category: 'nature'
  },
  {
    id: 9,
    name: 'Urban Bridge',
    url: 'https://picsum.photos/1920/1080?random=9',
    thumbnail: 'https://picsum.photos/400/225?random=9',
    category: 'urban'
  },
  {
    id: 10,
    name: 'Autumn Forest',
    url: 'https://picsum.photos/1920/1080?random=10',
    thumbnail: 'https://picsum.photos/400/225?random=10',
    category: 'nature'
  },
  {
    id: 11,
    name: 'Snow Mountains',
    url: 'https://picsum.photos/1920/1080?random=11',
    thumbnail: 'https://picsum.photos/400/225?random=11',
    category: 'nature'
  },
  {
    id: 12,
    name: 'River Valley',
    url: 'https://picsum.photos/1920/1080?random=12',
    thumbnail: 'https://picsum.photos/400/225?random=12',
    category: 'nature'
  },
  {
    id: 13,
    name: 'Night City',
    url: 'https://picsum.photos/1920/1080?random=13',
    thumbnail: 'https://picsum.photos/400/225?random=13',
    category: 'urban'
  },
  {
    id: 14,
    name: 'Tropical Beach',
    url: 'https://picsum.photos/1920/1080?random=14',
    thumbnail: 'https://picsum.photos/400/225?random=14',
    category: 'nature'
  },
  {
    id: 15,
    name: 'Industrial Landscape',
    url: 'https://picsum.photos/1920/1080?random=15',
    thumbnail: 'https://picsum.photos/400/225?random=15',
    category: 'urban'
  }
];

/**
 * Get background image by ID
 * @param {number} id - Image ID
 * @returns {object|null} Background image object or null if not found
 */
export const getBackgroundImageById = (id) => {
  return backgroundImages.find(image => image.id === id) || null;
};

/**
 * Get background images by category
 * @param {string} category - Image category ('nature' or 'urban')
 * @returns {array} Array of background images in the specified category
 */
export const getBackgroundImagesByCategory = (category) => {
  return backgroundImages.filter(image => image.category === category);
};
