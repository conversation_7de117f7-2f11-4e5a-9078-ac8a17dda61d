/**
 * Project colors data for solid color backgrounds
 */

export const projectColors = [
  { name: 'Blue', value: 'bg-blue-500', class: 'bg-blue-500' },
  { name: 'Green', value: 'bg-green-500', class: 'bg-green-500' },
  { name: 'Purple', value: 'bg-purple-500', class: 'bg-purple-500' },
  { name: 'Orange', value: 'bg-orange-500', class: 'bg-orange-500' },
  { name: 'Red', value: 'bg-red-500', class: 'bg-red-500' },
  { name: 'Pink', value: 'bg-pink-500', class: 'bg-pink-500' },
  { name: 'Indigo', value: 'bg-indigo-500', class: 'bg-indigo-500' },
  { name: 'Teal', value: 'bg-teal-500', class: 'bg-teal-500' },
  { name: '<PERSON><PERSON>', value: 'bg-cyan-500', class: 'bg-cyan-500' },
  { name: 'Emerald', value: 'bg-emerald-500', class: 'bg-emerald-500' },
  { name: 'Violet', value: 'bg-violet-500', class: 'bg-violet-500' },
  { name: '<PERSON>', value: 'bg-rose-500', class: 'bg-rose-500' }
];

/**
 * Get project color by value
 * @param {string} value - Color value (e.g., 'bg-blue-500')
 * @returns {object|null} Project color object or null if not found
 */
export const getProjectColorByValue = (value) => {
  return projectColors.find(color => color.value === value) || null;
};
